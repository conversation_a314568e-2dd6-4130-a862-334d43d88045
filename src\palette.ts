import { around } from "monkey-around";
import type RepeatLastCommands from "./main.ts";
import type { Command } from "obsidian";

function addCPListeners(plugin: RepeatLastCommands): void { // command palette
    addInfoPalette(plugin);
    addClickListener(plugin);
    setTimeout(() => { // delay to avoid conflict with repeat last commands shortcut (ctrl)
        addKeyboardListener(plugin);
    }, 800);
}

function onHKTrigger(plugin: RepeatLastCommands, id: string): void { // after shortcut
    const { modal } = getModalCmdVars(plugin);
    if (modal && !modal.win && plugin.settings.includeShortcuts && !shouldExcludeCommand(plugin, id)) {
        applySelectedId(id, plugin);
    }
}

export function onCommandTrigger(plugin: RepeatLastCommands): any {
    return around(plugin.app.commands, {
        executeCommand(originalMethod) {
            return function (...args: Command[]): any {
                if (args[0].id === "command-palette:open") {
                    addCPListeners(plugin);
                } else {
                    onHKTrigger(plugin, args[0].id);
                }
                const result = originalMethod && originalMethod.apply(this, args);
                return result;
            };
        },
    });
}

function shouldExcludeCommand(plugin: RepeatLastCommands, commandId: string): boolean {
    const userExcludedIDs = plugin.settings.userExcludedIDs || [];

    return (
        commandId === "repeat-last-commands:repeat-last-command" ||
        commandId === "repeat-last-commands:get-last-command" ||
        commandId === "repeat-last-commands:repeat-commands" ||
        (!plugin.settings.includeCmdPaletteOPen && commandId === "command-palette:open") ||
        userExcludedIDs.some(excludedID => commandId.startsWith(excludedID))
    );
}

function applySelectedId(id: string, plugin: RepeatLastCommands): void {
    // command
    const { lastCommands, settings } = plugin;
    plugin.lastCommand = id;

    // commands
    const maxEntries = settings.maxLastCmds;
    if (lastCommands.length > maxEntries) {
        lastCommands.shift();
    }
    lastCommands.push(id);
    plugin.lastCommands = [...new Set(lastCommands)];
    plugin.saveSettings();
}

function getModalCmdVars(plugin: RepeatLastCommands): { modal: any, instance: any, pluginCommand: any } {
    const pluginCommand = plugin.app.internalPlugins.getPluginById("command-palette");
    const instance = pluginCommand?.instance;
    const modal = instance?.modal;
    return { modal, instance, pluginCommand };
}

function addInfoPalette(plugin: RepeatLastCommands): void {
    const { modal } = getModalCmdVars(plugin);
    const resultContainerEl = modal?.resultContainerEl;

    if (resultContainerEl && !plugin.infoDiv) {
        plugin.infoDiv = document.createElement('div');
        plugin.infoDiv.classList.add('result-container-afterend');
        plugin.infoDiv.textContent = "Ctrl A: alias | Ctrl P: pin | Ctrl -: hide | Ctrl +: show | Ctrl h: hotkey";
        resultContainerEl.insertAdjacentElement("afterend", plugin.infoDiv);
    }
}

function addClickListener(plugin: RepeatLastCommands): void {
    const { modal } = getModalCmdVars(plugin);
    if (!modal?.chooser) return;

    const chooser = modal.chooser;
    const suggestions = chooser.suggestions;

    // Add click listener to capture command selection via mouse
    suggestions?.forEach((suggestion: HTMLElement, index: number) => {
        suggestion.addEventListener('click', () => {
            const selectedCommand = chooser.values[index];
            if (selectedCommand?.item?.id && !shouldExcludeCommand(plugin, selectedCommand.item.id)) {
                applySelectedId(selectedCommand.item.id, plugin);
            }
        });
    });
}

function addKeyboardListener(plugin: RepeatLastCommands): void {
    const { modal } = getModalCmdVars(plugin);
    if (!modal?.scope) return;

    // Add Enter key listener to capture command selection via keyboard
    modal.scope.register([], "Enter", () => {
        const chooser = modal.chooser;
        const selectedItem = chooser.selectedItem;
        const selectedCommand = chooser.values[selectedItem];

        if (selectedCommand?.item?.id && !shouldExcludeCommand(plugin, selectedCommand.item.id)) {
            applySelectedId(selectedCommand.item.id, plugin);
        }
    });
}