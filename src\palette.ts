import type { Command } from "obsidian";
import type RepeatLastCommands from "./main.ts";
import { around } from "monkey-around";
import type { RLCSettings } from "./types.ts";

export function onCommandTrigger(plugin: RepeatLastCommands): any {
    return around(plugin.app.commands, {
        executeCommand(originalMethod) {
            return function (...args: Command[]): any {
                if (args[0].id === "command-palette:open") {
                    plugin.uiManager.addInfoToPalette();
                }
                const result = originalMethod && originalMethod.apply(this, args);
                return result;
            };
        },
        executeCommandById(originalMethod) {
            return function (commandId: string, ...args: any[]): any {
                // Execute the command first
                const result = originalMethod && originalMethod.apply(this, [commandId, ...args]);

                // Debug logging
                console.log(`[RLC] Command executed: ${commandId}`);

                // Track the command if it's not excluded
                if (!shouldExcludeCommand(plugin.settings, commandId)) {
                    console.log(`[RLC] Adding to recent commands: ${commandId}`);
                    plugin.commandManager.addToRecentCommands(commandId);
                } else {
                    console.log(`[RLC] Command excluded: ${commandId}`);
                }

                return result;
            };
        },
    });
}

export function shouldExcludeCommand(settings: RLCSettings, commandId: string): boolean {
    return (
        commandId === "repeat-last-commands:repeat-last-command" ||
        commandId === "repeat-last-commands:repeat-commands" ||
        commandId === "repeat-last-commands:get-last-command" ||
        commandId === "command-palette:open" ||
        settings.excludeCommands.includes(commandId)
    );
}