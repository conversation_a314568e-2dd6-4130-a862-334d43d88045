import { Notice } from 'obsidian';
import type RepeatLastCommands from './main.ts';
import { getModalCmdVars } from './cmd-utils.ts';
import { LastCommandsModal } from './modals.ts';
import type { CommandPalettePluginInstance } from 'obsidian-typings';

export class CommandManager {
    private instance: CommandPalettePluginInstance;
    private recentCommands: string[] = [];
    private maxRecentCommands = 50; // Maximum number of recent commands to keep

    constructor(private plugin: RepeatLastCommands) {
        const { instance } = getModalCmdVars(this.plugin);
        this.instance = instance;

        // Initialize with saved recent commands from settings, fallback to command palette
        this.recentCommands = [...(this.plugin.settings.recentCommands || this.instance.recentCommands || [])];
    }

    public addToRecentCommands(commandId: string): void {
        // Remove the command if it already exists to avoid duplicates
        const existingIndex = this.recentCommands.indexOf(commandId);
        if (existingIndex !== -1) {
            this.recentCommands.splice(existingIndex, 1);
        }

        // Add to the beginning of the array
        this.recentCommands.unshift(commandId);

        // Keep only the most recent commands
        if (this.recentCommands.length > this.maxRecentCommands) {
            this.recentCommands = this.recentCommands.slice(0, this.maxRecentCommands);
        }

        // Save to settings
        this.saveRecentCommands();
    }

    private saveRecentCommands(): void {
        // Update settings but don't save immediately to avoid too many disk writes
        this.plugin.settings.recentCommands = [...this.recentCommands];

        // Debounce the save operation
        if (this.saveTimeout) {
            clearTimeout(this.saveTimeout);
        }
        this.saveTimeout = setTimeout(async () => {
            await this.plugin.saveSettings();
            this.saveTimeout = null;
        }, 1000); // Save after 1 second of inactivity
    }

    private saveTimeout: NodeJS.Timeout | null = null;

    public cleanup(): void {
        if (this.saveTimeout) {
            clearTimeout(this.saveTimeout);
            this.saveTimeout = null;
        }
        // Save immediately on cleanup
        this.plugin.settings.recentCommands = [...this.recentCommands];
        this.plugin.saveSettings();
    }

    public getRecentCommands(): string[] {
        return [...this.recentCommands];
    }

    public async executeLastCommand(): Promise<void> {
        const lastCommand = this.recentCommands[0];

        if (!lastCommand) {
            this.handleNoCommand();
        } else {
            new Notice(`Last command: ${lastCommand}`);
            this.plugin.app.commands.executeCommandById(lastCommand);
        }
    }

    public async showCommandsList(): Promise<void> {
        const lastCommands = this.recentCommands;

        if (lastCommands.length) {
            new LastCommandsModal(this.plugin).open();
        } else {
            this.handleNoCommand();
        }
    }

    public async copyLastCommandId(): Promise<void> {
        const lastCommand = this.recentCommands[0];

        if (lastCommand) {
            try {
                await navigator.clipboard.writeText(lastCommand);
                new Notice("Command id copied in clipboard");
            } catch (err) {
                console.error(err);
            }
        } else {
            new Notice("No last command");
        }
    }

    // Opens command palette by default when no command is found
    private handleNoCommand(): void {
        new Notice("No last command.\nOpening command palette...");

        setTimeout(() => {
            this.plugin.app.commands.executeCommandById("command-palette:open");
        }, 400);
    }
}